#!/bin/sh

GRADLE_VERSION=8.4
GRADLE_HOME="$HOME/.gradle/wrapper/dists/gradle-$GRADLE_VERSION"
GRADLE_ZIP="gradle-$GRADLE_VERSION-bin.zip"
GRADLE_URL="https://services.gradle.org/distributions/$GRADLE_ZIP"

if [ ! -d "$GRADLE_HOME" ]; then
    echo "Downloading Gradle $GRADLE_VERSION..."
    mkdir -p "$GRADLE_HOME"
    
    if command -v curl >/dev/null 2>&1; then
        curl -L "$GRADLE_URL" -o "/tmp/$GRADLE_ZIP"
    elif command -v wget >/dev/null 2>&1; then
        wget "$GRADLE_URL" -O "/tmp/$GRADLE_ZIP"
    else
        echo "Error: Neither curl nor wget found. Cannot download Gradle."
        exit 1
    fi
    
    if [ ! -f "/tmp/$GRADLE_ZIP" ]; then
        echo "Error: Failed to download Gradle"
        exit 1
    fi
    
    unzip "/tmp/$GRADLE_ZIP" -d "$GRADLE_HOME"
    rm "/tmp/$GRADLE_ZIP"
fi

GRADLE_BIN=$(find "$GRADLE_HOME" -name "gradle" -type f | head -1)

if [ -z "$GRADLE_BIN" ]; then
    echo "Error: Gradle executable not found"
    exit 1
fi

exec "$GRADLE_BIN" "$@"
