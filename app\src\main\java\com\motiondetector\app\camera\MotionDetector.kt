package com.motiondetector.app.camera

import android.graphics.Bitmap
import android.graphics.Color
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.nio.ByteBuffer
import kotlin.math.abs

class MotionDetector(
    private val motionThreshold: Float = 0.1f,
    private val minMotionPixels: Int = 1000
) : ImageAnalysis.Analyzer {
    
    private var previousFrame: IntArray? = null
    private var frameWidth = 0
    private var frameHeight = 0
    
    private val _motionDetected = MutableStateFlow(false)
    val motionDetected: StateFlow<Boolean> = _motionDetected
    
    private val _motionLevel = MutableStateFlow(0f)
    val motionLevel: StateFlow<Float> = _motionLevel
    
    override fun analyze(image: ImageProxy) {
        val currentFrame = convertImageToGrayscale(image)
        
        if (previousFrame != null && currentFrame != null) {
            val motionPixels = detectMotion(previousFrame!!, currentFrame)
            val motionPercentage = motionPixels.toFloat() / (frameWidth * frameHeight)
            
            _motionLevel.value = motionPercentage
            
            val hasMotion = motionPixels > minMotionPixels && motionPercentage > motionThreshold
            _motionDetected.value = hasMotion
        }
        
        previousFrame = currentFrame
        image.close()
    }
    
    private fun convertImageToGrayscale(image: ImageProxy): IntArray? {
        val buffer = image.planes[0].buffer
        val data = ByteArray(buffer.remaining())
        buffer.get(data)
        
        frameWidth = image.width
        frameHeight = image.height
        
        val grayscaleArray = IntArray(frameWidth * frameHeight)
        
        try {
            // Convert YUV to grayscale
            for (i in data.indices) {
                if (i < grayscaleArray.size) {
                    val gray = data[i].toInt() and 0xFF
                    grayscaleArray[i] = gray
                }
            }
            return grayscaleArray
        } catch (e: Exception) {
            return null
        }
    }
    
    private fun detectMotion(previousFrame: IntArray, currentFrame: IntArray): Int {
        var motionPixels = 0
        val threshold = 30 // Pixel difference threshold
        
        for (i in previousFrame.indices) {
            if (i < currentFrame.size) {
                val diff = abs(previousFrame[i] - currentFrame[i])
                if (diff > threshold) {
                    motionPixels++
                }
            }
        }
        
        return motionPixels
    }
    
    fun updateSensitivity(threshold: Float, minPixels: Int) {
        // Update motion detection parameters
    }
}
