package com.motiondetector.app.ui.screens

import android.content.Context
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.LifecycleOwner
import com.motiondetector.app.camera.CameraManager
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CameraScreen(
    onBackPressed: () -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val scope = rememberCoroutineScope()
    
    var cameraManager by remember { mutableStateOf<CameraManager?>(null) }
    var isRecording by remember { mutableStateOf(false) }
    var recordingStatus by remember { mutableStateOf("Monitoring for motion...") }
    
    // Initialize camera manager
    LaunchedEffect(Unit) {
        cameraManager = CameraManager(context)
    }
    
    // Observe motion detection
    cameraManager?.let { manager ->
        val motionDetected by manager.motionDetected.collectAsState()
        val motionLevel by manager.motionLevel.collectAsState()
        
        LaunchedEffect(motionDetected) {
            if (motionDetected && !isRecording) {
                recordingStatus = "Motion detected! Starting recording..."
                // TODO: Start video recording
                isRecording = true
            }
        }
        
        LaunchedEffect(motionLevel) {
            if (!isRecording) {
                recordingStatus = "Motion level: ${(motionLevel * 100).toInt()}%"
            }
        }
    }
    
    DisposableEffect(Unit) {
        onDispose {
            cameraManager?.release()
        }
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        // Camera Preview
        AndroidView(
            factory = { ctx ->
                PreviewView(ctx).apply {
                    scaleType = PreviewView.ScaleType.FILL_CENTER
                }
            },
            modifier = Modifier.fillMaxSize(),
            update = { previewView ->
                scope.launch {
                    cameraManager?.startCamera(lifecycleOwner, previewView)
                }
            }
        )
        
        // Top overlay with status
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .align(Alignment.TopCenter),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Motion Detector Active",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = recordingStatus,
                    style = MaterialTheme.typography.bodyMedium
                )
                
                if (isRecording) {
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .size(12.dp)
                                .clip(CircleShape)
                                .background(Color.Red)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "RECORDING",
                            style = MaterialTheme.typography.bodySmall,
                            fontWeight = FontWeight.Bold,
                            color = Color.Red
                        )
                    }
                }
            }
        }
        
        // Bottom controls
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .align(Alignment.BottomCenter),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Button(
                    onClick = onBackPressed,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondary
                    )
                ) {
                    Text("Back")
                }
                
                Button(
                    onClick = { /* TODO: Open settings */ },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.tertiary
                    )
                ) {
                    Text("Settings")
                }
                
                Button(
                    onClick = { 
                        isRecording = false
                        recordingStatus = "Recording stopped manually"
                    },
                    enabled = isRecording,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Red
                    )
                ) {
                    Text("Stop")
                }
            }
        }
    }
}
