package com.motiondetector.app.ui.screens

import android.content.Context
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.LifecycleOwner
import com.motiondetector.app.camera.CameraManager
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CameraScreen(
    onBackPressed: () -> Unit,
    onNavigateToGallery: () -> Unit = {},
    onNavigateToSettings: () -> Unit = {}
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val scope = rememberCoroutineScope()
    
    var cameraManager by remember { mutableStateOf<CameraManager?>(null) }
    var recordingStatus by remember { mutableStateOf("Monitoring for motion...") }
    var videoCount by remember { mutableStateOf(0) }
    
    // Initialize camera manager
    LaunchedEffect(Unit) {
        cameraManager = CameraManager(context)
    }
    
    // Observe motion detection and recording
    cameraManager?.let { manager ->
        val motionDetected by manager.motionDetected.collectAsState()
        val motionLevel by manager.motionLevel.collectAsState()
        val isRecording by manager.isRecording.collectAsState()
        val recordingDuration by manager.recordingDuration.collectAsState()
        val lastVideoFile by manager.lastVideoFile.collectAsState()

        LaunchedEffect(motionDetected) {
            if (motionDetected && !isRecording) {
                recordingStatus = "Motion detected! Starting recording..."
                manager.startRecording(20) // Record for 20 seconds
            }
        }

        LaunchedEffect(isRecording) {
            if (isRecording) {
                recordingStatus = "Recording in progress..."
            } else if (!motionDetected) {
                recordingStatus = "Monitoring for motion..."
            }
        }

        LaunchedEffect(recordingDuration) {
            if (isRecording) {
                val remaining = 20 - recordingDuration
                recordingStatus = "Recording... ${remaining}s remaining"
            }
        }

        LaunchedEffect(lastVideoFile) {
            lastVideoFile?.let {
                videoCount++
                recordingStatus = "Video saved! Total videos: $videoCount"
            }
        }

        LaunchedEffect(motionLevel) {
            if (!isRecording && !motionDetected) {
                recordingStatus = "Motion level: ${(motionLevel * 100).toInt()}%"
            }
        }
    }
    
    DisposableEffect(Unit) {
        onDispose {
            cameraManager?.release()
        }
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        // Camera Preview
        AndroidView(
            factory = { ctx ->
                PreviewView(ctx).apply {
                    scaleType = PreviewView.ScaleType.FILL_CENTER
                }
            },
            modifier = Modifier.fillMaxSize(),
            update = { previewView ->
                scope.launch {
                    cameraManager?.startCamera(lifecycleOwner, previewView)
                }
            }
        )
        
        // Top overlay with status
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .align(Alignment.TopCenter),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Motion Detector Active",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = recordingStatus,
                    style = MaterialTheme.typography.bodyMedium
                )
                
                if (isRecording) {
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .size(12.dp)
                                .clip(CircleShape)
                                .background(Color.Red)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "RECORDING",
                            style = MaterialTheme.typography.bodySmall,
                            fontWeight = FontWeight.Bold,
                            color = Color.Red
                        )
                    }
                }
            }
        }
        
        // Bottom controls
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .align(Alignment.BottomCenter),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Button(
                        onClick = onBackPressed,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary
                        )
                    ) {
                        Text("Back")
                    }

                    Button(
                        onClick = onNavigateToGallery,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.tertiary
                        )
                    ) {
                        Text("Videos ($videoCount)")
                    }

                    Button(
                        onClick = {
                            cameraManager?.stopRecording()
                            recordingStatus = "Recording stopped manually"
                        },
                        enabled = cameraManager?.isRecording?.collectAsState()?.value == true,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Red
                        )
                    ) {
                        Text("Stop")
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Button(
                        onClick = onNavigateToSettings,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        Text("Settings")
                    }
                }
            }
        }
    }
}
