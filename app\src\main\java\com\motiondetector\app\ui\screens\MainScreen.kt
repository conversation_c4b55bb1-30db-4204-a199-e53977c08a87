package com.motiondetector.app.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    hasCameraPermission: <PERSON>olean,
    hasAudioPermission: <PERSON>ole<PERSON>,
    hasStoragePermission: Boolean,
    onRequestPermissions: () -> Unit,
    onNavigateToCamera: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Motion Detector Camera",
                    style = MaterialTheme.typography.headlineMedium,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "Automatically detects movement and records 20-second videos",
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                if (!hasCameraPermission || !hasAudioPermission || !hasStoragePermission) {
                    PermissionSection(
                        hasCameraPermission = hasCameraPermission,
                        hasAudioPermission = hasAudioPermission,
                        hasStoragePermission = hasStoragePermission,
                        onRequestPermissions = onRequestPermissions
                    )
                } else {
                    Text(
                        text = "✅ All permissions granted!",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Button(
                        onClick = onNavigateToCamera,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("Start Motion Detection")
                    }
                }
            }
        }
    }
}

@Composable
private fun PermissionSection(
    hasCameraPermission: Boolean,
    hasAudioPermission: Boolean,
    hasStoragePermission: Boolean,
    onRequestPermissions: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Permissions Required:",
            style = MaterialTheme.typography.titleMedium
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        PermissionItem("Camera", hasCameraPermission)
        PermissionItem("Audio Recording", hasAudioPermission)
        PermissionItem("Storage", hasStoragePermission)
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = onRequestPermissions,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Grant Permissions")
        }
    }
}

@Composable
private fun PermissionItem(
    name: String,
    isGranted: Boolean
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = name,
            style = MaterialTheme.typography.bodyMedium
        )
        
        Text(
            text = if (isGranted) "✅" else "❌",
            style = MaterialTheme.typography.bodyMedium
        )
    }
}
