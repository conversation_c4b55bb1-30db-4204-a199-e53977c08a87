#!/bin/bash

# Motion Detector App - Development Environment Setup Script
# This script sets up the Podman container for Android development

set -e

echo "🚀 Setting up Motion Detector App Development Environment"
echo "=================================================="

# Check if podman is installed
if ! command -v podman &> /dev/null; then
    echo "❌ Podman is not installed. Please install Podman first."
    echo "Visit: https://podman.io/getting-started/installation"
    exit 1
fi

# Check if podman-compose is available
if ! command -v podman-compose &> /dev/null; then
    echo "⚠️  podman-compose not found. Installing..."
    pip3 install podman-compose
fi

echo "✅ Podman is available"

# Build the container
echo "🔨 Building Android development container..."
podman-compose build

echo "✅ Container built successfully"

# Create the container and start it
echo "🚀 Starting development container..."
podman-compose up -d

echo "✅ Development environment is ready!"
echo ""
echo "📋 Next steps:"
echo "1. Connect to the container: podman exec -it motion-detector-dev bash"
echo "2. Start developing your motion detection app"
echo "3. Use 'podman-compose down' to stop the environment"
echo ""
echo "🔧 Container includes:"
echo "   - Android SDK 34"
echo "   - Build tools and platform tools"
echo "   - Android emulator"
echo "   - Gradle build system"
echo "   - Java 17 OpenJDK"
echo ""
echo "Happy coding! 🎉"
