#!/usr/bin/env python3
"""Main development script for Motion Detector App"""

import sys
import subprocess
from pathlib import Path

def main():
    if len(sys.argv) < 2:
        print("🎯 Motion Detector App Development")
        print("=" * 40)
        print("Available commands:")
        print("  setup     - Build and start development environment")
        print("  shell     - Open shell in development container")
        print("  start     - Start development container")
        print("  stop      - Stop development container")
        print("  status    - Show container status")
        print("  clean     - Clean up containers and images")
        return
    
    command = sys.argv[1]
    
    if command == "setup":
        print("🚀 Setting up development environment...")
        subprocess.run([sys.executable, "container_manager.py", "build"])
        subprocess.run([sys.executable, "container_manager.py", "start"])
    elif command == "shell":
        subprocess.run([sys.executable, "container_manager.py", "shell"])
    elif command == "start":
        subprocess.run([sys.executable, "container_manager.py", "start"])
    elif command == "stop":
        subprocess.run([sys.executable, "container_manager.py", "stop"])
    elif command == "status":
        subprocess.run([sys.executable, "container_manager.py", "status"])
    elif command == "clean":
        print("🧹 Cleaning up containers...")
        subprocess.run([sys.executable, "container_manager.py", "stop"])
        subprocess.run(["podman", "system", "prune", "-f"])
    else:
        print(f"Unknown command: {command}")

if __name__ == "__main__":
    main()
