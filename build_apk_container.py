#!/usr/bin/env python3
"""
Build APK using <PERSON><PERSON> container environment
"""

import subprocess
import sys
import os
import shutil
import time
from pathlib import Path

class ContainerAPKBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.apk_output_dir = self.project_root / "apk_output"
        self.container_name = "motion-detector-dev"
        
    def check_podman(self):
        """Check if <PERSON><PERSON> is available"""
        print("🔍 Checking Podman availability...")
        try:
            result = subprocess.run(["podman", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ <PERSON>dman found: {result.stdout.strip()}")
                return True
            else:
                print("❌ Podman not working properly")
                return False
        except FileNotFoundError:
            print("❌ Podman not found. Please install <PERSON>dman first.")
            return False
    
    def setup_container(self):
        """Build and start the development container"""
        print("🏗️ Setting up development container...")
        
        # Build the container
        print("Building container image...")
        result = subprocess.run([
            "podman-compose", "build"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode != 0:
            print(f"❌ Container build failed: {result.stderr}")
            return False
        
        print("✅ Container built successfully")
        
        # Start the container
        print("Starting container...")
        result = subprocess.run([
            "podman-compose", "up", "-d"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode != 0:
            print(f"❌ Container start failed: {result.stderr}")
            return False
        
        print("✅ Container started successfully")
        
        # Wait a moment for container to be ready
        print("Waiting for container to be ready...")
        time.sleep(10)
        
        return True
    
    def check_container_status(self):
        """Check if container is running"""
        print("🔍 Checking container status...")
        try:
            result = subprocess.run([
                "podman", "ps", "--filter", f"name={self.container_name}", "--format", "{{.Status}}"
            ], capture_output=True, text=True)
            
            if result.returncode == 0 and "Up" in result.stdout:
                print("✅ Container is running")
                return True
            else:
                print("❌ Container is not running")
                return False
        except Exception as e:
            print(f"❌ Error checking container: {e}")
            return False
    
    def build_apk_in_container(self):
        """Build the APK inside the container"""
        print("🔨 Building APK in container...")
        
        # First, make sure we have the gradle wrapper
        print("Setting up Gradle wrapper in container...")
        setup_commands = [
            "cd /workspace",
            "chmod +x gradlew || true",
            "ls -la",
            "pwd"
        ]
        
        for cmd in setup_commands:
            result = subprocess.run([
                "podman", "exec", self.container_name, "bash", "-c", cmd
            ], capture_output=True, text=True)
            print(f"Command: {cmd}")
            print(f"Output: {result.stdout}")
            if result.stderr:
                print(f"Error: {result.stderr}")
        
        # Build the APK
        print("Running Gradle build...")
        build_command = "cd /workspace && ./gradlew assembleDebug --no-daemon --stacktrace"
        
        result = subprocess.run([
            "podman", "exec", self.container_name, "bash", "-c", build_command
        ], timeout=600, text=True)
        
        if result.returncode == 0:
            print("✅ APK built successfully in container")
            return True
        else:
            print("❌ APK build failed in container")
            # Try to get more info about the failure
            self.debug_container()
            return False
    
    def debug_container(self):
        """Debug container issues"""
        print("🔍 Debugging container...")
        
        debug_commands = [
            "cd /workspace && ls -la",
            "cd /workspace && find . -name '*.apk' 2>/dev/null || echo 'No APK found'",
            "cd /workspace && ls -la app/build/outputs/apk/debug/ 2>/dev/null || echo 'Debug dir not found'",
            "java -version",
            "which gradle || echo 'Gradle not found'",
            "echo $ANDROID_HOME",
            "ls -la $ANDROID_HOME 2>/dev/null || echo 'Android SDK not found'"
        ]
        
        for cmd in debug_commands:
            print(f"\n--- Running: {cmd} ---")
            result = subprocess.run([
                "podman", "exec", self.container_name, "bash", "-c", cmd
            ], capture_output=True, text=True)
            print(result.stdout)
            if result.stderr:
                print(f"Error: {result.stderr}")
    
    def copy_apk_from_container(self):
        """Copy the built APK from container to host"""
        print("📦 Copying APK from container...")
        
        # Create output directory
        self.apk_output_dir.mkdir(exist_ok=True)
        
        # Look for APK in container
        find_apk_cmd = "find /workspace -name '*.apk' -type f"
        result = subprocess.run([
            "podman", "exec", self.container_name, "bash", "-c", find_apk_cmd
        ], capture_output=True, text=True)
        
        if result.returncode != 0 or not result.stdout.strip():
            print("❌ No APK found in container")
            return False
        
        apk_paths = result.stdout.strip().split('\n')
        print(f"Found APK files: {apk_paths}")
        
        # Copy the first APK found (usually the debug APK)
        container_apk_path = apk_paths[0]
        host_apk_path = self.apk_output_dir / "MotionDetector.apk"
        
        copy_result = subprocess.run([
            "podman", "cp", f"{self.container_name}:{container_apk_path}", str(host_apk_path)
        ], capture_output=True, text=True)
        
        if copy_result.returncode == 0:
            print(f"✅ APK copied to: {host_apk_path}")
            
            # Get file size
            file_size = host_apk_path.stat().st_size / 1024 / 1024
            print(f"📱 APK size: {file_size:.1f} MB")
            
            return True
        else:
            print(f"❌ Failed to copy APK: {copy_result.stderr}")
            return False
    
    def cleanup_container(self):
        """Stop the container"""
        print("🧹 Cleaning up container...")
        subprocess.run(["podman-compose", "down"], capture_output=True)
        print("✅ Container stopped")
    
    def build(self):
        """Main build process"""
        print("🚀 Building Motion Detector APK with Podman")
        print("=" * 50)
        
        try:
            # Check prerequisites
            if not self.check_podman():
                return False
            
            # Setup and start container
            if not self.setup_container():
                return False
            
            # Verify container is running
            if not self.check_container_status():
                return False
            
            # Build APK in container
            if not self.build_apk_in_container():
                return False
            
            # Copy APK to host
            if not self.copy_apk_from_container():
                return False
            
            print("\n🎉 APK Build Successful!")
            print(f"📱 APK Location: {self.apk_output_dir / 'MotionDetector.apk'}")
            print("\n📋 Installation Instructions:")
            print("1. Transfer MotionDetector.apk to your Android device")
            print("2. Enable 'Install from unknown sources' in Android settings")
            print("3. Tap the APK file to install")
            print("4. Grant camera, audio, and storage permissions when prompted")
            print("5. Configure your email address in the app settings")
            
            return True
            
        except KeyboardInterrupt:
            print("\n⚠️ Build cancelled by user")
            return False
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
            return False
        finally:
            # Always cleanup
            self.cleanup_container()

if __name__ == "__main__":
    builder = ContainerAPKBuilder()
    success = builder.build()
    sys.exit(0 if success else 1)
