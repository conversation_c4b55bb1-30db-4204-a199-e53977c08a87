#!/usr/bin/env python3
"""
Simple APK builder for Motion Detector App
"""

import subprocess
import sys
import os
import urllib.request
import zipfile
import shutil
from pathlib import Path

def download_file(url, destination):
    """Download a file from URL"""
    print(f"Downloading {url}...")
    try:
        urllib.request.urlretrieve(url, destination)
        return True
    except Exception as e:
        print(f"Download failed: {e}")
        return False

def extract_zip(zip_path, extract_to):
    """Extract a zip file"""
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        return True
    except Exception as e:
        print(f"Extraction failed: {e}")
        return False

def setup_gradle():
    """Download and setup Gradle"""
    gradle_version = "8.4"
    gradle_dir = Path.home() / ".gradle" / "wrapper" / "dists" / f"gradle-{gradle_version}"
    
    if gradle_dir.exists():
        print("✅ Gradle already installed")
        return find_gradle_executable(gradle_dir)
    
    print("📦 Setting up Gradle...")
    gradle_dir.mkdir(parents=True, exist_ok=True)
    
    gradle_url = f"https://services.gradle.org/distributions/gradle-{gradle_version}-bin.zip"
    gradle_zip = gradle_dir / f"gradle-{gradle_version}-bin.zip"
    
    if not download_file(gradle_url, gradle_zip):
        return None
    
    if not extract_zip(gradle_zip, gradle_dir):
        return None
    
    gradle_zip.unlink()  # Remove zip file
    
    return find_gradle_executable(gradle_dir)

def find_gradle_executable(gradle_dir):
    """Find the Gradle executable"""
    if os.name == 'nt':
        gradle_exe = "gradle.bat"
    else:
        gradle_exe = "gradle"
    
    for root, dirs, files in os.walk(gradle_dir):
        if gradle_exe in files:
            return Path(root) / gradle_exe
    
    return None

def build_apk():
    """Build the APK"""
    print("🏗️ Building Motion Detector APK...")
    
    # Setup Gradle
    gradle_path = setup_gradle()
    if not gradle_path:
        print("❌ Failed to setup Gradle")
        return False
    
    print(f"✅ Using Gradle: {gradle_path}")
    
    # Build the APK
    try:
        print("🔨 Running Gradle build...")
        result = subprocess.run([
            str(gradle_path), 
            "assembleDebug",
            "--no-daemon",
            "--stacktrace"
        ], cwd=Path.cwd(), timeout=600, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Build successful!")
            return copy_apk()
        else:
            print(f"❌ Build failed:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Build timed out")
        return False
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def copy_apk():
    """Copy the built APK to output directory"""
    print("📦 Looking for built APK...")
    
    # Create output directory
    output_dir = Path.cwd() / "apk_output"
    output_dir.mkdir(exist_ok=True)
    
    # Look for the APK
    apk_paths = [
        Path.cwd() / "app" / "build" / "outputs" / "apk" / "debug" / "app-debug.apk",
        Path.cwd() / "app" / "build" / "outputs" / "apk" / "app-debug.apk"
    ]
    
    for apk_path in apk_paths:
        if apk_path.exists():
            output_apk = output_dir / "MotionDetector.apk"
            shutil.copy2(apk_path, output_apk)
            
            print(f"✅ APK created: {output_apk}")
            print(f"📱 APK size: {output_apk.stat().st_size / 1024 / 1024:.1f} MB")
            
            print("\n🎉 APK Build Complete!")
            print("\n📋 Installation Instructions:")
            print("1. Transfer MotionDetector.apk to your Android device")
            print("2. Enable 'Install from unknown sources' in Android settings")
            print("3. Tap the APK file to install")
            print("4. Grant permissions when prompted")
            
            return True
    
    print("❌ APK not found in build output")
    return False

def check_prerequisites():
    """Check if we have everything needed"""
    print("🔍 Checking prerequisites...")
    
    # Check Java
    try:
        result = subprocess.run(["java", "-version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Java found")
        else:
            print("❌ Java not found - please install Java 17 or later")
            return False
    except FileNotFoundError:
        print("❌ Java not found - please install Java 17 or later")
        return False
    
    # Check Android project structure
    required_files = [
        "app/build.gradle.kts",
        "app/src/main/AndroidManifest.xml",
        "settings.gradle.kts"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ Missing required file: {file_path}")
            return False
    
    print("✅ All prerequisites met")
    return True

def main():
    print("🚀 Motion Detector APK Builder")
    print("=" * 40)
    
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please fix the issues above.")
        return 1
    
    if build_apk():
        print("\n🎉 Success! Your APK is ready to install.")
        return 0
    else:
        print("\n❌ Build failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
