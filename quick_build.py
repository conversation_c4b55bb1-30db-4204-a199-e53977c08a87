#!/usr/bin/env python3
"""
Quick APK build using existing container
"""

import subprocess
import sys
import time
from pathlib import Path

def run_command(cmd, description, timeout=300):
    """Run a command with timeout"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode == 0:
            print(f"✅ {description} - Success")
            return True, result.stdout
        else:
            print(f"❌ {description} - Failed")
            print(f"Error: {result.stderr}")
            return False, result.stderr
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - Timeout")
        return False, "Timeout"
    except Exception as e:
        print(f"❌ {description} - Error: {e}")
        return False, str(e)

def main():
    print("🚀 Quick APK Build for Motion Detector")
    print("=" * 40)
    
    # Check if container is running
    success, output = run_command("podman ps --filter name=motion-detector-dev", "Checking container status")
    if not success or "motion-detector-dev" not in output:
        print("📦 Starting container...")
        success, _ = run_command("podman-compose up -d", "Starting container", 120)
        if not success:
            print("❌ Failed to start container")
            return 1
        
        # Wait for container to be ready
        print("⏳ Waiting for container to be ready...")
        time.sleep(15)
    
    # Create a simple Gradle build script in the container
    print("📝 Creating build script in container...")
    build_script = '''#!/bin/bash
cd /workspace
echo "Current directory: $(pwd)"
echo "Files in workspace:"
ls -la

# Check if Android SDK is available
echo "Checking Android SDK..."
echo "ANDROID_HOME: $ANDROID_HOME"
ls -la $ANDROID_HOME 2>/dev/null || echo "Android SDK not found"

# Check Java
echo "Java version:"
java -version

# Try to build with gradle
echo "Building APK..."
if [ -f "./gradlew" ]; then
    chmod +x ./gradlew
    ./gradlew assembleDebug --no-daemon --stacktrace
else
    echo "gradlew not found, trying with gradle command"
    gradle assembleDebug --no-daemon --stacktrace
fi

echo "Build completed. Looking for APK..."
find . -name "*.apk" -type f
'''
    
    # Write build script to container
    success, _ = run_command(
        f'echo \'{build_script}\' | podman exec -i motion-detector-dev tee /tmp/build.sh',
        "Creating build script"
    )
    if not success:
        return 1
    
    # Make script executable and run it
    success, _ = run_command(
        "podman exec motion-detector-dev chmod +x /tmp/build.sh",
        "Making build script executable"
    )
    if not success:
        return 1
    
    # Run the build
    print("🔨 Running build in container...")
    success, output = run_command(
        "podman exec motion-detector-dev /tmp/build.sh",
        "Building APK",
        600  # 10 minutes timeout
    )
    
    if success:
        print("✅ Build completed successfully!")
        print("Build output:")
        print(output)
        
        # Try to copy APK
        print("📦 Copying APK from container...")
        
        # Create output directory
        output_dir = Path("apk_output")
        output_dir.mkdir(exist_ok=True)
        
        # Find and copy APK
        success, apk_paths = run_command(
            "podman exec motion-detector-dev find /workspace -name '*.apk' -type f",
            "Finding APK files"
        )
        
        if success and apk_paths.strip():
            apk_path = apk_paths.strip().split('\n')[0]
            print(f"Found APK: {apk_path}")
            
            success, _ = run_command(
                f"podman cp motion-detector-dev:{apk_path} apk_output/MotionDetector.apk",
                "Copying APK to host"
            )
            
            if success:
                apk_file = Path("apk_output/MotionDetector.apk")
                if apk_file.exists():
                    file_size = apk_file.stat().st_size / 1024 / 1024
                    print(f"\n🎉 APK Build Successful!")
                    print(f"📱 APK Location: {apk_file.absolute()}")
                    print(f"📏 APK Size: {file_size:.1f} MB")
                    print("\n📋 Installation Instructions:")
                    print("1. Transfer MotionDetector.apk to your Android device")
                    print("2. Enable 'Install from unknown sources' in Android settings")
                    print("3. Tap the APK file to install")
                    print("4. Grant permissions when prompted")
                    return 0
        
        print("❌ Failed to copy APK")
        return 1
    else:
        print("❌ Build failed")
        print("Build output:")
        print(output)
        return 1

if __name__ == "__main__":
    sys.exit(main())
