#!/usr/bin/env python3
"""
Motion Detector App - Development Environment Setup
Creates a Python virtual environment and manages containerized Android development
"""

import os
import sys
import subprocess
import venv
from pathlib import Path

class MotionDetectorDevSetup:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.venv_path = self.project_root / "venv"
        self.requirements_file = self.project_root / "requirements.txt"
        
    def create_venv(self):
        """Create Python virtual environment"""
        print("🐍 Creating Python virtual environment...")
        venv.create(self.venv_path, with_pip=True)
        print("✅ Virtual environment created")
        
    def get_venv_python(self):
        """Get path to Python executable in venv"""
        if os.name == 'nt':  # Windows
            return self.venv_path / "Scripts" / "python.exe"
        else:  # Unix/Linux
            return self.venv_path / "bin" / "python"
            
    def get_venv_pip(self):
        """Get path to pip executable in venv"""
        if os.name == 'nt':  # Windows
            return self.venv_path / "Scripts" / "pip.exe"
        else:  # Unix/Linux
            return self.venv_path / "bin" / "pip"
    
    def create_requirements(self):
        """Create requirements.txt for container management"""
        requirements = [
            "podman-compose>=1.0.6",
            "docker-compose>=1.29.0",  # Fallback if podman-compose not available
            "pyyaml>=6.0",
            "requests>=2.28.0",
            "click>=8.0.0",
        ]
        
        with open(self.requirements_file, 'w') as f:
            f.write('\n'.join(requirements))
        print("✅ Requirements file created")
    
    def install_dependencies(self):
        """Install Python dependencies in venv"""
        print("📦 Installing Python dependencies...")
        pip_path = self.get_venv_pip()
        subprocess.run([str(pip_path), "install", "-r", str(self.requirements_file)], check=True)
        print("✅ Dependencies installed")
    
    def create_dev_scripts(self):
        """Create development management scripts"""
        self.create_container_manager()
        self.create_android_dev_script()
        print("✅ Development scripts created")
    
    def create_container_manager(self):
        """Create container management script"""
        script_content = '''#!/usr/bin/env python3
"""Container Manager for Motion Detector App Development"""

import subprocess
import sys
import os
from pathlib import Path

class ContainerManager:
    def __init__(self):
        self.project_root = Path(__file__).parent
        
    def run_command(self, cmd):
        """Run shell command"""
        try:
            result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
            return result.stdout
        except subprocess.CalledProcessError as e:
            print(f"❌ Error: {e.stderr}")
            return None
    
    def build_container(self):
        """Build the Android development container"""
        print("🔨 Building Android development container...")
        if self.run_command("podman-compose build"):
            print("✅ Container built successfully")
        else:
            print("❌ Failed to build container")
    
    def start_container(self):
        """Start the development container"""
        print("🚀 Starting development container...")
        if self.run_command("podman-compose up -d"):
            print("✅ Container started successfully")
            print("📋 Connect with: python dev.py shell")
        else:
            print("❌ Failed to start container")
    
    def stop_container(self):
        """Stop the development container"""
        print("🛑 Stopping development container...")
        if self.run_command("podman-compose down"):
            print("✅ Container stopped")
        else:
            print("❌ Failed to stop container")
    
    def shell(self):
        """Open shell in container"""
        print("🐚 Opening shell in development container...")
        os.system("podman exec -it motion-detector-dev bash")
    
    def status(self):
        """Show container status"""
        print("📊 Container status:")
        self.run_command("podman-compose ps")

if __name__ == "__main__":
    manager = ContainerManager()
    
    if len(sys.argv) < 2:
        print("Usage: python container_manager.py [build|start|stop|shell|status]")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "build":
        manager.build_container()
    elif command == "start":
        manager.start_container()
    elif command == "stop":
        manager.stop_container()
    elif command == "shell":
        manager.shell()
    elif command == "status":
        manager.status()
    else:
        print(f"Unknown command: {command}")
        print("Available commands: build, start, stop, shell, status")
'''
        
        with open(self.project_root / "container_manager.py", 'w') as f:
            f.write(script_content)
    
    def create_android_dev_script(self):
        """Create main development script"""
        script_content = '''#!/usr/bin/env python3
"""Main development script for Motion Detector App"""

import sys
import subprocess
from pathlib import Path

def main():
    if len(sys.argv) < 2:
        print("🎯 Motion Detector App Development")
        print("=" * 40)
        print("Available commands:")
        print("  setup     - Build and start development environment")
        print("  shell     - Open shell in development container")
        print("  start     - Start development container")
        print("  stop      - Stop development container")
        print("  status    - Show container status")
        print("  clean     - Clean up containers and images")
        return
    
    command = sys.argv[1]
    
    if command == "setup":
        print("🚀 Setting up development environment...")
        subprocess.run([sys.executable, "container_manager.py", "build"])
        subprocess.run([sys.executable, "container_manager.py", "start"])
    elif command == "shell":
        subprocess.run([sys.executable, "container_manager.py", "shell"])
    elif command == "start":
        subprocess.run([sys.executable, "container_manager.py", "start"])
    elif command == "stop":
        subprocess.run([sys.executable, "container_manager.py", "stop"])
    elif command == "status":
        subprocess.run([sys.executable, "container_manager.py", "status"])
    elif command == "clean":
        print("🧹 Cleaning up containers...")
        subprocess.run([sys.executable, "container_manager.py", "stop"])
        subprocess.run(["podman", "system", "prune", "-f"])
    else:
        print(f"Unknown command: {command}")

if __name__ == "__main__":
    main()
'''
        
        with open(self.project_root / "dev.py", 'w') as f:
            f.write(script_content)
    
    def setup(self):
        """Run complete setup"""
        print("🎯 Motion Detector App - Development Setup")
        print("=" * 50)
        
        if not self.venv_path.exists():
            self.create_venv()
        else:
            print("✅ Virtual environment already exists")
        
        self.create_requirements()
        self.install_dependencies()
        self.create_dev_scripts()
        
        print("\n🎉 Setup complete!")
        print("\n📋 Next steps:")
        print("1. Activate virtual environment:")
        if os.name == 'nt':
            print("   venv\\Scripts\\activate")
        else:
            print("   source venv/bin/activate")
        print("2. Set up development environment: python dev.py setup")
        print("3. Open development shell: python dev.py shell")

if __name__ == "__main__":
    setup = MotionDetectorDevSetup()
    setup.setup()
