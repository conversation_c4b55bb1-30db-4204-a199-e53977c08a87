# Motion Detector Camera App

An Android app that detects movement in front of the camera, records 20-second videos, and sends them via Gmail.

## 🎯 Features

- **Motion Detection**: Automatically detects movement using camera feed
- **Video Recording**: Records 20-second videos when motion is detected
- **Email Integration**: Sends videos via Gmail using phone's Google account
- **Containerized Development**: Complete development environment in containers
- **No Host Installation**: Everything runs in Python venv + containers

## 🚀 Quick Start

### Prerequisites

- **Python 3.8+** (only requirement on your system)
- **Podman** (for containers)

### Setup (Windows)

```bash
# Run the setup
setup.bat

# Activate virtual environment
venv\Scripts\activate

# Set up development environment
python dev.py setup

# Open development shell
python dev.py shell
```

### Setup (Linux/Mac)

```bash
# Make setup script executable
chmod +x setup.sh

# Run the setup
./setup.sh

# Activate virtual environment
source venv/bin/activate

# Set up development environment
python dev.py setup

# Open development shell
python dev.py shell
```

## 🛠️ Development Commands

Once your virtual environment is activated:

```bash
# Set up containers and start development
python dev.py setup

# Open shell in development container
python dev.py shell

# Start development container
python dev.py start

# Stop development container
python dev.py stop

# Check container status
python dev.py status

# Clean up containers
python dev.py clean
```

## 📁 Project Structure

```
mobil_cam/
├── venv/                    # Python virtual environment
├── app/                     # Android app source code (created later)
├── Dockerfile              # Android development container
├── docker-compose.yml      # Container orchestration
├── setup.py                # Main setup script
├── dev.py                  # Development management script
├── container_manager.py    # Container operations
├── setup.bat               # Windows quick setup
├── setup.sh                # Linux/Mac quick setup
└── README.md               # This file
```

## 🔧 Development Environment

The containerized environment includes:

- **Android SDK 34** with build tools
- **Android Emulator** for testing
- **Gradle** build system
- **Java 17 OpenJDK**
- **Kotlin** support
- **Camera and motion detection libraries**

## 📱 App Architecture

The motion detector app will include:

1. **Camera Preview**: Real-time camera feed display
2. **Motion Detection**: Frame comparison algorithm
3. **Video Recording**: 20-second video capture
4. **Gmail Integration**: Email sending functionality
5. **Settings UI**: Configuration and preferences

## 🎮 Next Steps

After setup is complete:

1. The development environment will be ready
2. Android project structure will be created
3. Motion detection algorithm will be implemented
4. Video recording functionality will be added
5. Gmail integration will be configured
6. UI and settings will be created

## 🐛 Troubleshooting

### Container Issues
- Ensure Podman is installed and running
- Check container status: `python dev.py status`
- Restart containers: `python dev.py stop && python dev.py start`

### Python Environment Issues
- Ensure Python 3.8+ is installed
- Recreate venv: Delete `venv/` folder and run setup again

### Development Issues
- Open container shell: `python dev.py shell`
- Check Android SDK: `sdkmanager --list`
- Verify emulator: `avdmanager list avd`

## 📄 License

This project is for educational and personal use.
