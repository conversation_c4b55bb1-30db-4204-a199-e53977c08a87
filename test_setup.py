#!/usr/bin/env python3
"""
Test script to verify the Motion Detector App development setup
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"🔍 Testing: {description}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
            return True
        else:
            print(f"❌ {description} - FAILED")
            print(f"Error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - TIMEOUT")
        return False
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False

def check_file_exists(file_path, description):
    """Check if a file exists"""
    print(f"🔍 Checking: {description}")
    if Path(file_path).exists():
        print(f"✅ {description} - EXISTS")
        return True
    else:
        print(f"❌ {description} - MISSING")
        return False

def main():
    print("🎯 Motion Detector App - Setup Verification")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Check Python virtual environment
    total_tests += 1
    if check_file_exists("venv", "Python virtual environment"):
        tests_passed += 1
    
    # Test 2: Check if venv Python works
    total_tests += 1
    if os.name == 'nt':  # Windows
        python_cmd = "venv\\Scripts\\python.exe --version"
    else:  # Unix/Linux
        python_cmd = "venv/bin/python --version"
    
    if run_command(python_cmd, "Virtual environment Python"):
        tests_passed += 1
    
    # Test 3: Check required Python packages
    total_tests += 1
    if os.name == 'nt':
        pip_cmd = "venv\\Scripts\\pip.exe list"
    else:
        pip_cmd = "venv/bin/pip list"
    
    if run_command(pip_cmd, "Python packages installation"):
        tests_passed += 1
    
    # Test 4: Check Android project structure
    android_files = [
        "app/build.gradle.kts",
        "app/src/main/AndroidManifest.xml",
        "app/src/main/java/com/motiondetector/app/MainActivity.kt",
        "settings.gradle.kts",
        "build.gradle.kts"
    ]
    
    for file_path in android_files:
        total_tests += 1
        if check_file_exists(file_path, f"Android file: {file_path}"):
            tests_passed += 1
    
    # Test 5: Check container files
    container_files = [
        "Dockerfile",
        "docker-compose.yml",
        "dev.py",
        "container_manager.py"
    ]
    
    for file_path in container_files:
        total_tests += 1
        if check_file_exists(file_path, f"Container file: {file_path}"):
            tests_passed += 1
    
    # Test 6: Check development scripts
    total_tests += 1
    if os.name == 'nt':
        dev_cmd = "venv\\Scripts\\python.exe dev.py"
    else:
        dev_cmd = "venv/bin/python dev.py"
    
    if run_command(dev_cmd, "Development script execution"):
        tests_passed += 1
    
    # Test 7: Check if Podman is available (optional)
    total_tests += 1
    if run_command("podman --version", "Podman availability (optional)"):
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Your development environment is ready.")
        print("\n📋 Next steps:")
        print("1. Activate virtual environment")
        print("2. Run: python dev.py setup")
        print("3. Run: python dev.py shell")
        print("4. Start developing your motion detection app!")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the setup.")
        print("\n🔧 Troubleshooting:")
        print("- Ensure Python 3.8+ is installed")
        print("- Run setup.py again if virtual environment issues")
        print("- Check if all files were created properly")
        return 1

if __name__ == "__main__":
    sys.exit(main())
