package com.motiondetector.app.camera

import android.content.Context
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import kotlinx.coroutines.flow.StateFlow
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import com.motiondetector.app.video.VideoRecorder

class CameraManager(private val context: Context) {
    
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var preview: Preview? = null
    private var imageAnalyzer: ImageAnalysis? = null
    private var videoCapture: VideoCapture<Recorder>? = null
    
    private val motionDetector = MotionDetector()
    private val videoRecorder = VideoRecorder(context)
    private val cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()

    val motionDetected: StateFlow<Boolean> = motionDetector.motionDetected
    val motionLevel: StateFlow<Float> = motionDetector.motionLevel
    val isRecording: StateFlow<Boolean> = videoRecorder.isRecording
    val recordingDuration: StateFlow<Int> = videoRecorder.recordingDuration
    val lastVideoFile = videoRecorder.lastVideoFile
    
    suspend fun startCamera(
        lifecycleOwner: LifecycleOwner,
        previewView: PreviewView
    ): Boolean {
        return try {
            val cameraProvider = ProcessCameraProvider.getInstance(context).get()
            this.cameraProvider = cameraProvider
            
            // Preview
            preview = Preview.Builder().build().also {
                it.setSurfaceProvider(previewView.surfaceProvider)
            }
            
            // Image analyzer for motion detection
            imageAnalyzer = ImageAnalysis.Builder()
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .build()
                .also {
                    it.setAnalyzer(cameraExecutor, motionDetector)
                }
            
            // Video capture
            val recorder = Recorder.Builder()
                .setQualitySelector(QualitySelector.from(Quality.HD))
                .build()
            videoCapture = VideoCapture.withOutput(recorder)
            
            // Select back camera as a default
            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
            
            // Unbind use cases before rebinding
            cameraProvider.unbindAll()
            
            // Bind use cases to camera
            camera = cameraProvider.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageAnalyzer,
                videoCapture
            )
            
            true
        } catch (exc: Exception) {
            false
        }
    }
    
    fun stopCamera() {
        cameraProvider?.unbindAll()
        camera = null
        preview = null
        imageAnalyzer = null
        videoCapture = null
    }
    
    fun getVideoCapture(): VideoCapture<Recorder>? = videoCapture
    
    fun updateMotionSensitivity(threshold: Float, minPixels: Int) {
        motionDetector.updateSensitivity(threshold, minPixels)
    }
    
    fun release() {
        stopCamera()
        cameraExecutor.shutdown()
    }
}
