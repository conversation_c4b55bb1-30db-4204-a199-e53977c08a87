package com.motiondetector.app.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.motiondetector.app.data.SettingsRepository

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onBackPressed: () -> Unit
) {
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    
    var emailAddress by remember { mutableStateOf("") }
    var motionSensitivity by remember { mutableFloatStateOf(0.1f) }
    var recordingDuration by remember { mutableIntStateOf(20) }
    var autoEmailEnabled by remember { mutableStateOf(false) }
    var emailSubject by remember { mutableStateOf("Motion Detected - Video Alert") }
    
    // Load settings
    LaunchedEffect(Unit) {
        emailAddress = settingsRepository.getEmailAddress()
        motionSensitivity = settingsRepository.getMotionSensitivity()
        recordingDuration = settingsRepository.getRecordingDuration()
        autoEmailEnabled = settingsRepository.isAutoEmailEnabled()
        emailSubject = settingsRepository.getEmailSubject()
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Button(onClick = onBackPressed) {
                Text("Back")
            }
            
            Text(
                text = "Settings",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            
            Button(
                onClick = {
                    // Save all settings
                    settingsRepository.saveEmailAddress(emailAddress)
                    settingsRepository.saveMotionSensitivity(motionSensitivity)
                    settingsRepository.saveRecordingDuration(recordingDuration)
                    settingsRepository.saveAutoEmailEnabled(autoEmailEnabled)
                    settingsRepository.saveEmailSubject(emailSubject)
                }
            ) {
                Text("Save")
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Email Settings Section
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Email Settings",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                OutlinedTextField(
                    value = emailAddress,
                    onValueChange = { emailAddress = it },
                    label = { Text("Email Address") },
                    placeholder = { Text("<EMAIL>") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                OutlinedTextField(
                    value = emailSubject,
                    onValueChange = { emailSubject = it },
                    label = { Text("Email Subject") },
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Auto-send emails",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    
                    Switch(
                        checked = autoEmailEnabled,
                        onCheckedChange = { autoEmailEnabled = it }
                    )
                }
                
                if (autoEmailEnabled && emailAddress.isBlank()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Please enter an email address to enable auto-sending",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Motion Detection Settings Section
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Motion Detection",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "Motion Sensitivity: ${(motionSensitivity * 100).toInt()}%",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Slider(
                    value = motionSensitivity,
                    onValueChange = { motionSensitivity = it },
                    valueRange = 0.01f..0.5f,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Text(
                    text = "Lower values = more sensitive",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Recording Settings Section
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Recording Settings",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "Recording Duration: ${recordingDuration} seconds",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Slider(
                    value = recordingDuration.toFloat(),
                    onValueChange = { recordingDuration = it.toInt() },
                    valueRange = 5f..60f,
                    steps = 10,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Text(
                    text = "Duration of video recording when motion is detected",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Info Section
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "How it works",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "• The app continuously monitors the camera for motion\n" +
                            "• When motion is detected, it automatically records a video\n" +
                            "• If auto-email is enabled, the video is sent to your email\n" +
                            "• All videos are saved locally and can be viewed in the gallery",
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}
