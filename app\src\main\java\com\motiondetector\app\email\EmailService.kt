package com.motiondetector.app.email

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

class EmailService(private val context: Context) {
    
    private val _emailStatus = MutableStateFlow<EmailStatus>(EmailStatus.Idle)
    val emailStatus: StateFlow<EmailStatus> = _emailStatus
    
    sealed class EmailStatus {
        object Idle : EmailStatus()
        object Sending : EmailStatus()
        object Success : EmailStatus()
        data class Error(val message: String) : EmailStatus()
    }
    
    suspend fun sendVideoViaGmail(
        videoFile: File,
        recipientEmail: String,
        subject: String = "Motion Detected - Video Alert"
    ): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                _emailStatus.value = EmailStatus.Sending
                
                val success = sendViaGmailIntent(videoFile, recipientEmail, subject)
                
                _emailStatus.value = if (success) {
                    EmailStatus.Success
                } else {
                    EmailStatus.Error("Failed to open Gmail")
                }
                
                success
            } catch (e: Exception) {
                _emailStatus.value = EmailStatus.Error(e.message ?: "Unknown error")
                false
            }
        }
    }
    
    private fun sendViaGmailIntent(
        videoFile: File,
        recipientEmail: String,
        subject: String
    ): Boolean {
        return try {
            val uri = FileProvider.getUriForFile(
                context,
                "com.motiondetector.app.fileprovider",
                videoFile
            )
            
            val emailBody = buildEmailBody(videoFile)
            
            val intent = Intent(Intent.ACTION_SEND).apply {
                type = "video/mp4"
                setPackage("com.google.android.gm") // Specifically target Gmail
                putExtra(Intent.EXTRA_EMAIL, arrayOf(recipientEmail))
                putExtra(Intent.EXTRA_SUBJECT, subject)
                putExtra(Intent.EXTRA_TEXT, emailBody)
                putExtra(Intent.EXTRA_STREAM, uri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            // Check if Gmail is available
            val packageManager = context.packageManager
            val activities = packageManager.queryIntentActivities(intent, 0)
            
            if (activities.isNotEmpty()) {
                context.startActivity(intent)
                true
            } else {
                // Fallback to general email intent
                sendViaGeneralEmailIntent(videoFile, recipientEmail, subject)
            }
        } catch (e: Exception) {
            false
        }
    }
    
    private fun sendViaGeneralEmailIntent(
        videoFile: File,
        recipientEmail: String,
        subject: String
    ): Boolean {
        return try {
            val uri = FileProvider.getUriForFile(
                context,
                "com.motiondetector.app.fileprovider",
                videoFile
            )
            
            val emailBody = buildEmailBody(videoFile)
            
            val intent = Intent(Intent.ACTION_SEND).apply {
                type = "video/mp4"
                putExtra(Intent.EXTRA_EMAIL, arrayOf(recipientEmail))
                putExtra(Intent.EXTRA_SUBJECT, subject)
                putExtra(Intent.EXTRA_TEXT, emailBody)
                putExtra(Intent.EXTRA_STREAM, uri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            val chooserIntent = Intent.createChooser(intent, "Send video via email")
            chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            
            context.startActivity(chooserIntent)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    private fun buildEmailBody(videoFile: File): String {
        val timestamp = SimpleDateFormat("MMM dd, yyyy 'at' HH:mm:ss", Locale.getDefault())
            .format(Date(videoFile.lastModified()))
        
        val fileSize = formatFileSize(videoFile.length())
        
        return """
            Motion Detected Alert
            
            A motion detection event was recorded by your Motion Detector app.
            
            Video Details:
            • Recorded: $timestamp
            • File size: $fileSize
            • Duration: ~20 seconds
            
            Please find the video attached to this email.
            
            Sent automatically by Motion Detector App
        """.trimIndent()
    }
    
    private fun formatFileSize(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        
        return when {
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$bytes B"
        }
    }
    
    fun resetStatus() {
        _emailStatus.value = EmailStatus.Idle
    }
    
    fun isGmailAvailable(): Boolean {
        return try {
            val intent = Intent(Intent.ACTION_SEND).apply {
                type = "video/mp4"
                setPackage("com.google.android.gm")
            }
            
            val packageManager = context.packageManager
            val activities = packageManager.queryIntentActivities(intent, 0)
            activities.isNotEmpty()
        } catch (e: Exception) {
            false
        }
    }
}
