@echo off
setlocal

set GRADLE_VERSION=8.4
set GRADLE_HOME=%USERPROFILE%\.gradle\wrapper\dists\gradle-%GRADLE_VERSION%
set GRADLE_ZIP=gradle-%GRADLE_VERSION%-bin.zip
set GRADLE_URL=https://services.gradle.org/distributions/%GRADLE_ZIP%

if not exist "%GRADLE_HOME%" (
    echo Downloading Gradle %GRADLE_VERSION%...
    mkdir "%GRADLE_HOME%" 2>nul
    powershell -Command "try { Invoke-WebRequest -Uri '%GRADLE_URL%' -OutFile '%TEMP%\%GRADLE_ZIP%' } catch { exit 1 }"
    if errorlevel 1 (
        echo Failed to download Gradle
        exit /b 1
    )
    powershell -Command "try { Expand-Archive -Path '%TEMP%\%GRADLE_ZIP%' -DestinationPath '%GRADLE_HOME%' -Force } catch { exit 1 }"
    if errorlevel 1 (
        echo Failed to extract Gradle
        exit /b 1
    )
    del "%TEMP%\%GRADLE_ZIP%" 2>nul
)

for /r "%GRADLE_HOME%" %%i in (gradle.bat) do (
    if exist "%%i" (
        set GRADLE_BIN=%%i
        goto found
    )
)

echo Gradle executable not found
exit /b 1

:found
"%GRADLE_BIN%" %*
