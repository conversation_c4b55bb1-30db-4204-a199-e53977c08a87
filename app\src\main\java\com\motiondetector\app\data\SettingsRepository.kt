package com.motiondetector.app.data

import android.content.Context
import android.content.SharedPreferences

class SettingsRepository(context: Context) {
    
    private val sharedPreferences: SharedPreferences = context.getSharedPreferences(
        "motion_detector_settings",
        Context.MODE_PRIVATE
    )
    
    companion object {
        private const val KEY_EMAIL_ADDRESS = "email_address"
        private const val KEY_MOTION_SENSITIVITY = "motion_sensitivity"
        private const val KEY_RECORDING_DURATION = "recording_duration"
        private const val KEY_AUTO_EMAIL_ENABLED = "auto_email_enabled"
        private const val KEY_EMAIL_SUBJECT = "email_subject"
        
        // Default values
        private const val DEFAULT_MOTION_SENSITIVITY = 0.1f
        private const val DEFAULT_RECORDING_DURATION = 20
        private const val DEFAULT_AUTO_EMAIL_ENABLED = false
        private const val DEFAULT_EMAIL_SUBJECT = "Motion Detected - Video Alert"
    }
    
    // Email settings
    fun saveEmailAddress(email: String) {
        sharedPreferences.edit()
            .putString(KEY_EMAIL_ADDRESS, email)
            .apply()
    }
    
    fun getEmailAddress(): String {
        return sharedPreferences.getString(KEY_EMAIL_ADDRESS, "") ?: ""
    }
    
    fun saveEmailSubject(subject: String) {
        sharedPreferences.edit()
            .putString(KEY_EMAIL_SUBJECT, subject)
            .apply()
    }
    
    fun getEmailSubject(): String {
        return sharedPreferences.getString(KEY_EMAIL_SUBJECT, DEFAULT_EMAIL_SUBJECT) ?: DEFAULT_EMAIL_SUBJECT
    }
    
    fun saveAutoEmailEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_AUTO_EMAIL_ENABLED, enabled)
            .apply()
    }
    
    fun isAutoEmailEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_AUTO_EMAIL_ENABLED, DEFAULT_AUTO_EMAIL_ENABLED)
    }
    
    // Motion detection settings
    fun saveMotionSensitivity(sensitivity: Float) {
        sharedPreferences.edit()
            .putFloat(KEY_MOTION_SENSITIVITY, sensitivity)
            .apply()
    }
    
    fun getMotionSensitivity(): Float {
        return sharedPreferences.getFloat(KEY_MOTION_SENSITIVITY, DEFAULT_MOTION_SENSITIVITY)
    }
    
    // Recording settings
    fun saveRecordingDuration(duration: Int) {
        sharedPreferences.edit()
            .putInt(KEY_RECORDING_DURATION, duration)
            .apply()
    }
    
    fun getRecordingDuration(): Int {
        return sharedPreferences.getInt(KEY_RECORDING_DURATION, DEFAULT_RECORDING_DURATION)
    }
    
    // Utility methods
    fun hasEmailConfigured(): Boolean {
        return getEmailAddress().isNotBlank()
    }
    
    fun shouldAutoSendEmail(): Boolean {
        return isAutoEmailEnabled() && hasEmailConfigured()
    }
    
    fun clearAllSettings() {
        sharedPreferences.edit().clear().apply()
    }
    
    fun exportSettings(): Map<String, Any> {
        return mapOf(
            KEY_EMAIL_ADDRESS to getEmailAddress(),
            KEY_MOTION_SENSITIVITY to getMotionSensitivity(),
            KEY_RECORDING_DURATION to getRecordingDuration(),
            KEY_AUTO_EMAIL_ENABLED to isAutoEmailEnabled(),
            KEY_EMAIL_SUBJECT to getEmailSubject()
        )
    }
    
    fun importSettings(settings: Map<String, Any>) {
        val editor = sharedPreferences.edit()
        
        settings[KEY_EMAIL_ADDRESS]?.let { 
            if (it is String) editor.putString(KEY_EMAIL_ADDRESS, it)
        }
        settings[KEY_MOTION_SENSITIVITY]?.let { 
            if (it is Float) editor.putFloat(KEY_MOTION_SENSITIVITY, it)
        }
        settings[KEY_RECORDING_DURATION]?.let { 
            if (it is Int) editor.putInt(KEY_RECORDING_DURATION, it)
        }
        settings[KEY_AUTO_EMAIL_ENABLED]?.let { 
            if (it is Boolean) editor.putBoolean(KEY_AUTO_EMAIL_ENABLED, it)
        }
        settings[KEY_EMAIL_SUBJECT]?.let { 
            if (it is String) editor.putString(KEY_EMAIL_SUBJECT, it)
        }
        
        editor.apply()
    }
}
