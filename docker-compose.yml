version: '3.8'

services:
  android-dev:
    build: .
    container_name: motion-detector-dev
    volumes:
      - .:/workspace
      - android-gradle-cache:/home/<USER>/.gradle
      - android-sdk-cache:/opt/android-sdk
    ports:
      - "5037:5037"  # ADB port
      - "8080:8080"  # For any web interfaces
    environment:
      - DISPLAY=${DISPLAY:-:0}
      - ANDROID_HOME=/opt/android-sdk
      - ANDROID_SDK_ROOT=/opt/android-sdk
    stdin_open: true
    tty: true
    privileged: true  # Needed for emulator
    devices:
      - /dev/kvm:/dev/kvm  # For hardware acceleration (if available)
    networks:
      - android-network

volumes:
  android-gradle-cache:
  android-sdk-cache:

networks:
  android-network:
    driver: bridge
