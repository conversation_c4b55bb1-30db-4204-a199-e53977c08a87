#!/usr/bin/env python3
"""
Build script for Motion Detector Android APK
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path

class APKBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "build_output"
        self.apk_output_dir = self.project_root / "apk_output"
        
    def setup_build_environment(self):
        """Set up the build environment"""
        print("🔧 Setting up build environment...")
        
        # Create output directories
        self.build_dir.mkdir(exist_ok=True)
        self.apk_output_dir.mkdir(exist_ok=True)
        
        # Check if we have the Android project
        if not (self.project_root / "app" / "build.gradle.kts").exists():
            print("❌ Android project not found!")
            return False
            
        print("✅ Build environment ready")
        return True
    
    def build_with_container(self):
        """Build APK using the containerized environment"""
        print("🏗️ Building APK in container...")
        
        try:
            # Start the container if not running
            print("Starting development container...")
            result = subprocess.run([
                sys.executable, "container_manager.py", "start"
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode != 0:
                print(f"❌ Failed to start container: {result.stderr}")
                return False
            
            # Build the APK inside container
            print("Building APK inside container...")
            build_command = [
                "podman", "exec", "-it", "motion-detector-dev",
                "bash", "-c", 
                "cd /workspace && ./gradlew assembleDebug"
            ]
            
            result = subprocess.run(build_command, timeout=600)
            
            if result.returncode == 0:
                print("✅ APK built successfully in container")
                return True
            else:
                print("❌ APK build failed in container")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Build timed out")
            return False
        except Exception as e:
            print(f"❌ Build error: {e}")
            return False
    
    def build_locally(self):
        """Build APK locally (fallback method)"""
        print("🏗️ Building APK locally...")
        
        try:
            # Check if gradlew exists
            gradlew_path = self.project_root / "gradlew.bat" if os.name == 'nt' else self.project_root / "gradlew"
            
            if not gradlew_path.exists():
                print("Creating Gradle wrapper...")
                self.create_gradle_wrapper()
            
            # Make gradlew executable on Unix systems
            if os.name != 'nt':
                os.chmod(gradlew_path, 0o755)
            
            # Build the APK
            build_command = [str(gradlew_path), "assembleDebug"]
            
            print("Running Gradle build...")
            result = subprocess.run(
                build_command, 
                cwd=self.project_root,
                timeout=600,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("✅ APK built successfully")
                return True
            else:
                print(f"❌ APK build failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Build timed out")
            return False
        except Exception as e:
            print(f"❌ Build error: {e}")
            return False
    
    def create_gradle_wrapper(self):
        """Create Gradle wrapper if it doesn't exist"""
        print("Creating Gradle wrapper...")
        
        # Create gradlew script for Unix
        gradlew_content = '''#!/bin/sh
# Gradle wrapper script

GRADLE_VERSION=8.4
GRADLE_ZIP="gradle-${GRADLE_VERSION}-bin.zip"
GRADLE_URL="https://services.gradle.org/distributions/${GRADLE_ZIP}"
GRADLE_HOME="$HOME/.gradle/wrapper/dists/gradle-${GRADLE_VERSION}"

# Download Gradle if not exists
if [ ! -d "$GRADLE_HOME" ]; then
    echo "Downloading Gradle ${GRADLE_VERSION}..."
    mkdir -p "$GRADLE_HOME"
    curl -L "$GRADLE_URL" -o "/tmp/${GRADLE_ZIP}"
    unzip "/tmp/${GRADLE_ZIP}" -d "$GRADLE_HOME"
    rm "/tmp/${GRADLE_ZIP}"
fi

# Find Gradle executable
GRADLE_BIN=$(find "$GRADLE_HOME" -name "gradle" -type f | head -1)

# Execute Gradle
exec "$GRADLE_BIN" "$@"
'''
        
        # Create gradlew.bat for Windows
        gradlew_bat_content = '''@echo off
set GRADLE_VERSION=8.4
set GRADLE_ZIP=gradle-%GRADLE_VERSION%-bin.zip
set GRADLE_URL=https://services.gradle.org/distributions/%GRADLE_ZIP%
set GRADLE_HOME=%USERPROFILE%\\.gradle\\wrapper\\dists\\gradle-%GRADLE_VERSION%

if not exist "%GRADLE_HOME%" (
    echo Downloading Gradle %GRADLE_VERSION%...
    mkdir "%GRADLE_HOME%"
    powershell -Command "Invoke-WebRequest -Uri '%GRADLE_URL%' -OutFile '%TEMP%\\%GRADLE_ZIP%'"
    powershell -Command "Expand-Archive -Path '%TEMP%\\%GRADLE_ZIP%' -DestinationPath '%GRADLE_HOME%'"
    del "%TEMP%\\%GRADLE_ZIP%"
)

for /r "%GRADLE_HOME%" %%i in (gradle.bat) do set GRADLE_BIN=%%i

"%GRADLE_BIN%" %*
'''
        
        # Write the appropriate wrapper
        if os.name == 'nt':
            with open(self.project_root / "gradlew.bat", 'w') as f:
                f.write(gradlew_bat_content)
        else:
            with open(self.project_root / "gradlew", 'w') as f:
                f.write(gradlew_content)
            os.chmod(self.project_root / "gradlew", 0o755)
    
    def copy_apk_output(self):
        """Copy the built APK to output directory"""
        print("📦 Copying APK to output directory...")
        
        # Look for the APK in the build directory
        apk_paths = [
            self.project_root / "app" / "build" / "outputs" / "apk" / "debug" / "app-debug.apk",
            self.project_root / "app" / "build" / "outputs" / "apk" / "app-debug.apk"
        ]
        
        for apk_path in apk_paths:
            if apk_path.exists():
                output_apk = self.apk_output_dir / "MotionDetector.apk"
                shutil.copy2(apk_path, output_apk)
                print(f"✅ APK copied to: {output_apk}")
                return str(output_apk)
        
        print("❌ APK file not found in build output")
        return None
    
    def build(self):
        """Main build process"""
        print("🚀 Starting Motion Detector APK Build")
        print("=" * 50)
        
        if not self.setup_build_environment():
            return False
        
        # Try container build first, then local build
        success = False
        
        # Check if container is available
        try:
            result = subprocess.run(["podman", "--version"], capture_output=True)
            if result.returncode == 0:
                print("📦 Container environment detected, trying container build...")
                success = self.build_with_container()
        except FileNotFoundError:
            print("📦 Podman not found, using local build...")
        
        if not success:
            print("🔄 Falling back to local build...")
            success = self.build_locally()
        
        if success:
            apk_path = self.copy_apk_output()
            if apk_path:
                print("\n🎉 APK Build Successful!")
                print(f"📱 APK Location: {apk_path}")
                print("\n📋 Installation Instructions:")
                print("1. Transfer the APK to your Android device")
                print("2. Enable 'Install from unknown sources' in Android settings")
                print("3. Open the APK file on your device to install")
                print("4. Grant camera, audio, and storage permissions when prompted")
                return True
        
        print("\n❌ APK Build Failed!")
        print("Please check the error messages above and try again.")
        return False

if __name__ == "__main__":
    builder = APKBuilder()
    success = builder.build()
    sys.exit(0 if success else 1)
