package com.motiondetector.app.video

import android.content.Context
import android.os.Environment
import androidx.camera.core.VideoCapture
import androidx.camera.video.*
import androidx.core.content.ContextCompat
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

class VideoRecorder(private val context: Context) {
    
    private var recording: Recording? = null
    private var videoCapture: VideoCapture<Recorder>? = null
    
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording
    
    private val _recordingDuration = MutableStateFlow(0)
    val recordingDuration: StateFlow<Int> = _recordingDuration
    
    private val _lastVideoFile = MutableStateFlow<File?>(null)
    val lastVideoFile: StateFlow<File?> = _lastVideoFile
    
    fun setVideoCapture(videoCapture: VideoCapture<Recorder>) {
        this.videoCapture = videoCapture
    }
    
    fun startRecording(durationSeconds: Int = 20): Boolean {
        val videoCapture = this.videoCapture ?: return false
        
        if (_isRecording.value) {
            return false // Already recording
        }
        
        val videoFile = createVideoFile()
        val outputOptions = FileOutputOptions.Builder(videoFile).build()
        
        recording = videoCapture.output
            .prepareRecording(context, outputOptions)
            .apply {
                // Enable audio recording if permission is granted
                withAudioEnabled()
            }
            .start(ContextCompat.getMainExecutor(context)) { recordEvent ->
                when (recordEvent) {
                    is VideoRecordEvent.Start -> {
                        _isRecording.value = true
                        _recordingDuration.value = 0
                    }
                    is VideoRecordEvent.Finalize -> {
                        _isRecording.value = false
                        if (!recordEvent.hasError()) {
                            _lastVideoFile.value = videoFile
                        }
                    }
                    is VideoRecordEvent.Status -> {
                        // Update recording duration
                        val duration = recordEvent.recordingStats.recordedDurationNanos
                        _recordingDuration.value = TimeUnit.NANOSECONDS.toSeconds(duration).toInt()
                        
                        // Stop recording after specified duration
                        if (_recordingDuration.value >= durationSeconds) {
                            stopRecording()
                        }
                    }
                }
            }
        
        return true
    }
    
    fun stopRecording() {
        recording?.stop()
        recording = null
    }
    
    private fun createVideoFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "motion_detected_$timeStamp.mp4"
        
        // Create videos directory in external files
        val videosDir = File(context.getExternalFilesDir(Environment.DIRECTORY_MOVIES), "MotionDetector")
        if (!videosDir.exists()) {
            videosDir.mkdirs()
        }
        
        return File(videosDir, fileName)
    }
    
    fun getVideoFiles(): List<File> {
        val videosDir = File(context.getExternalFilesDir(Environment.DIRECTORY_MOVIES), "MotionDetector")
        return if (videosDir.exists()) {
            videosDir.listFiles { file -> file.extension == "mp4" }?.toList() ?: emptyList()
        } else {
            emptyList()
        }
    }
    
    fun deleteVideoFile(file: File): Boolean {
        return try {
            file.delete()
        } catch (e: Exception) {
            false
        }
    }
    
    fun getVideoFileSize(file: File): Long {
        return file.length()
    }
    
    fun formatFileSize(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        
        return when {
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$bytes B"
        }
    }
}
