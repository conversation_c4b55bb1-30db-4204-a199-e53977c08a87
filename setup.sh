#!/bin/bash

echo "🎯 Motion Detector App - Quick Setup"
echo "====================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed"
    echo "Please install Python 3.8+ from your package manager"
    exit 1
fi

echo "✅ Python found"
echo "🚀 Running setup..."

python3 setup.py

echo ""
echo "📋 To continue development:"
echo "1. Run: source venv/bin/activate"
echo "2. Run: python dev.py setup"
echo "3. Run: python dev.py shell"
