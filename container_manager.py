#!/usr/bin/env python3
"""Container Manager for Motion Detector App Development"""

import subprocess
import sys
import os
from pathlib import Path

class ContainerManager:
    def __init__(self):
        self.project_root = Path(__file__).parent
        
    def run_command(self, cmd):
        """Run shell command"""
        try:
            result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
            return result.stdout
        except subprocess.CalledProcessError as e:
            print(f"❌ Error: {e.stderr}")
            return None
    
    def build_container(self):
        """Build the Android development container"""
        print("Building Android development container...")
        if self.run_command("podman-compose build"):
            print("Container built successfully")
        else:
            print("Failed to build container")
    
    def start_container(self):
        """Start the development container"""
        print("Starting development container...")
        if self.run_command("podman-compose up -d"):
            print("Container started successfully")
            print("Connect with: python dev.py shell")
        else:
            print("Failed to start container")
    
    def stop_container(self):
        """Stop the development container"""
        print("Stopping development container...")
        if self.run_command("podman-compose down"):
            print("Container stopped")
        else:
            print("Failed to stop container")

    def shell(self):
        """Open shell in container"""
        print("Opening shell in development container...")
        os.system("podman exec -it motion-detector-dev bash")

    def status(self):
        """Show container status"""
        print("Container status:")
        self.run_command("podman-compose ps")

if __name__ == "__main__":
    manager = ContainerManager()
    
    if len(sys.argv) < 2:
        print("Usage: python container_manager.py [build|start|stop|shell|status]")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "build":
        manager.build_container()
    elif command == "start":
        manager.start_container()
    elif command == "stop":
        manager.stop_container()
    elif command == "shell":
        manager.shell()
    elif command == "status":
        manager.status()
    else:
        print(f"Unknown command: {command}")
        print("Available commands: build, start, stop, shell, status")
