# Android Development Container with Motion Detection App Support
FROM ubuntu:22.04

# Set environment variables
ENV ANDROID_HOME=/opt/android-sdk
ENV ANDROID_SDK_ROOT=/opt/android-sdk
ENV PATH=${PATH}:${ANDROID_HOME}/cmdline-tools/latest/bin:${ANDROID_HOME}/platform-tools:${ANDROID_HOME}/emulator
ENV JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64

# Install system dependencies
RUN apt-get update && apt-get install -y \
    openjdk-17-jdk \
    wget \
    unzip \
    git \
    curl \
    build-essential \
    libgl1-mesa-dev \
    libpulse0 \
    libxrandr2 \
    libasound2-dev \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0 \
    libxss1 \
    libgconf-2-4 \
    libxtst6 \
    libxrandr2 \
    libasound2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Create android-sdk directory
RUN mkdir -p ${ANDROID_HOME}

# Download and install Android Command Line Tools
RUN wget -q https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip -O /tmp/cmdline-tools.zip && \
    unzip -q /tmp/cmdline-tools.zip -d ${ANDROID_HOME} && \
    mv ${ANDROID_HOME}/cmdline-tools ${ANDROID_HOME}/cmdline-tools-temp && \
    mkdir -p ${ANDROID_HOME}/cmdline-tools/latest && \
    mv ${ANDROID_HOME}/cmdline-tools-temp/* ${ANDROID_HOME}/cmdline-tools/latest/ && \
    rm -rf ${ANDROID_HOME}/cmdline-tools-temp && \
    rm /tmp/cmdline-tools.zip

# Accept Android SDK licenses
RUN yes | sdkmanager --licenses

# Install Android SDK components
RUN sdkmanager "platform-tools" \
    "platforms;android-34" \
    "platforms;android-33" \
    "build-tools;34.0.0" \
    "build-tools;33.0.2" \
    "system-images;android-34;google_apis;x86_64" \
    "emulator" \
    "cmake;3.22.1" \
    "ndk;25.2.9519653"

# Install Gradle
RUN wget -q https://services.gradle.org/distributions/gradle-8.4-bin.zip -O /tmp/gradle.zip && \
    unzip -q /tmp/gradle.zip -d /opt && \
    ln -s /opt/gradle-8.4/bin/gradle /usr/local/bin/gradle && \
    rm /tmp/gradle.zip

# Create working directory
WORKDIR /workspace

# Create non-root user for development
RUN useradd -m -s /bin/bash developer && \
    chown -R developer:developer /opt/android-sdk && \
    chown -R developer:developer /workspace

# Switch to developer user
USER developer

# Set up Android Virtual Device (AVD)
RUN echo "no" | avdmanager create avd -n "MotionDetectorAVD" -k "system-images;android-34;google_apis;x86_64" -d "pixel_4"

# Expose ADB port
EXPOSE 5037

# Default command
CMD ["/bin/bash"]
